# LAQ模型推理配置文件
# 该配置文件定义了LAQ模型推理的所有参数设置

# 模型配置
model:
  # 模型架构参数
  dim: 1024                    # 模型维度
  quant_dim: 32               # 量化维度
  codebook_size: 8            # 码本大小
  image_size: 256             # 输入图像尺寸
  patch_size: 32              # 图像块大小
  spatial_depth: 8            # 空间Transformer层数
  temporal_depth: 8           # 时间Transformer层数
  dim_head: 64                # 注意力头维度
  heads: 16                   # 注意力头数量
  code_seq_len: 4             # 代码序列长度
  channels: 3                 # 输入图像通道数
  
  # 模型文件路径
  checkpoint_path: "models/laq_openx.pt"  # 预训练模型路径

# 推理配置
inference:
  batch_size: 32              # 批处理大小
  num_workers: 4              # 数据加载器工作进程数
  pin_memory: true            # 是否使用内存锁定（GPU加速）
  
# 数据配置
data:
  # 输入数据路径
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  
  # 输出结果路径
  output_file: "results/laq_inference_results.jsonl"
  
  # 数据预处理参数
  image_transforms:
    resize: 256               # 图像缩放尺寸
    normalize: false          # 是否标准化
    
# 日志配置
logging:
  level: "INFO"               # 日志级别: DEBUG, INFO, WARNING, ERROR
  file: "laq_inference.log"   # 日志文件路径
  console: true               # 是否输出到控制台
  
# 性能配置
performance:
  use_cuda: true              # 是否使用CUDA（如果可用）
  mixed_precision: false      # 是否使用混合精度
  
# 输出配置
output:
  save_intermediate: false    # 是否保存中间结果
  result_format: "jsonl"      # 结果格式: jsonl, json
  include_metadata: true      # 是否包含元数据
  
# 高级配置
advanced:
  # 内存管理
  max_memory_usage: "8GB"     # 最大内存使用量
  
  # 错误处理
  continue_on_error: true     # 遇到错误时是否继续处理
  max_retries: 3              # 最大重试次数
  
  # 进度显示
  progress_bar: true          # 是否显示进度条
  update_frequency: 10        # 进度更新频率（批次）

# 预设配置（可选择使用）
presets:
  # 快速推理配置（适合测试）
  fast:
    model:
      spatial_depth: 4
      temporal_depth: 4
      codebook_size: 8
    inference:
      batch_size: 64
      
  # 高质量推理配置（适合生产）
  high_quality:
    model:
      spatial_depth: 12
      temporal_depth: 12
      codebook_size: 1024
    inference:
      batch_size: 16
      
  # 大规模推理配置（适合大数据集）
  large_scale:
    model:
      codebook_size: 512
    inference:
      batch_size: 128
      num_workers: 8
