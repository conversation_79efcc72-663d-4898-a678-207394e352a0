#!/usr/bin/env python3
"""
测试修复后的LAQ推理脚本

该脚本用于验证修复后的推理脚本是否能正确处理不同尺寸的图像。
"""

import os
import sys
import json
import tempfile
from pathlib import Path
import subprocess

import torch
from PIL import Image
import numpy as np

def create_mixed_size_test_data():
    """创建包含不同尺寸图像的测试数据"""
    print("📝 创建混合尺寸测试数据...")
    
    test_dir = Path("test_mixed_size_data")
    test_dir.mkdir(exist_ok=True)
    
    # 创建不同尺寸的测试图像
    test_cases = [
        ("256x256", (256, 256)),
        ("256x411", (256, 411)),
        ("300x300", (300, 300)),
        ("224x224", (224, 224)),
    ]
    
    test_data = []
    
    for i, (size_name, (h, w)) in enumerate(test_cases):
        # 创建随机图像
        img1 = Image.fromarray(np.random.randint(0, 255, (h, w, 3), dtype=np.uint8))
        img2 = Image.fromarray(np.random.randint(0, 255, (h, w, 3), dtype=np.uint8))
        
        img1_path = test_dir / f"test_frame1_{size_name}.jpg"
        img2_path = test_dir / f"test_frame2_{size_name}.jpg"
        
        img1.save(img1_path)
        img2.save(img2_path)
        
        test_data.append({
            "id": f"test_{size_name}_{i:03d}",
            "image": str(img1_path),
            "next_image": str(img2_path),
            "instruction": f"Predict action for {size_name} images",
            "vision": "",
            "fields": "[instruction],[vision],delta"
        })
        
        print(f"  ✅ 创建 {size_name} 图像对")
    
    # 保存测试数据
    test_jsonl = test_dir / "mixed_size_input.jsonl"
    with open(test_jsonl, 'w') as f:
        for item in test_data:
            f.write(json.dumps(item) + '\n')
    
    print(f"  ✅ 测试数据保存到: {test_jsonl}")
    return test_dir

def test_inference_with_mixed_sizes():
    """测试混合尺寸推理"""
    print("\n🧪 测试混合尺寸推理...")
    
    # 创建测试数据
    test_dir = create_mixed_size_test_data()
    
    # 运行推理
    input_file = test_dir / "mixed_size_input.jsonl"
    output_file = test_dir / "mixed_size_output.jsonl"
    
    cmd = [
        'python3', 'scripts/inference/laq_inference.py',
        '--input', str(input_file),
        '--output', str(output_file),
        '--batch_size', '2',
        '--log_level', 'INFO'
    ]
    
    print(f"  执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("  ✅ 推理执行成功")
            
            # 检查输出文件
            if output_file.exists():
                with open(output_file, 'r') as f:
                    results = [json.loads(line) for line in f if line.strip()]
                
                print(f"  ✅ 生成了 {len(results)} 个结果")
                
                # 显示结果示例
                for i, result in enumerate(results[:2]):
                    print(f"  - 结果 {i+1}: ID={result['id']}, Delta={result['delta']}")
                
                return True
            else:
                print("  ❌ 输出文件不存在")
                return False
        else:
            print(f"  ❌ 推理执行失败")
            print(f"  错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ 推理执行超时")
        return False
    except Exception as e:
        print(f"  ❌ 推理执行异常: {e}")
        return False
    finally:
        # 清理测试数据
        cleanup_test_data(test_dir)

def cleanup_test_data(test_dir):
    """清理测试数据"""
    try:
        import shutil
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"🧹 清理测试数据: {test_dir}")
    except Exception as e:
        print(f"⚠️  清理失败: {e}")

def test_single_batch():
    """测试单个批次推理"""
    print("\n🔬 测试单个批次推理...")
    
    # 创建简单测试数据
    test_dir = Path("test_single_batch")
    test_dir.mkdir(exist_ok=True)
    
    # 创建一个256x411的图像（已知会导致问题的尺寸）
    img1 = Image.fromarray(np.random.randint(0, 255, (256, 411, 3), dtype=np.uint8))
    img2 = Image.fromarray(np.random.randint(0, 255, (256, 411, 3), dtype=np.uint8))
    
    img1_path = test_dir / "frame1.jpg"
    img2_path = test_dir / "frame2.jpg"
    
    img1.save(img1_path)
    img2.save(img2_path)
    
    # 创建测试数据
    test_data = [{
        "id": "single_test_001",
        "image": str(img1_path),
        "next_image": str(img2_path),
        "instruction": "Test single batch with 256x411 images",
        "vision": "",
        "fields": "[instruction],[vision],delta"
    }]
    
    test_jsonl = test_dir / "single_input.jsonl"
    with open(test_jsonl, 'w') as f:
        for item in test_data:
            f.write(json.dumps(item) + '\n')
    
    # 运行推理
    output_file = test_dir / "single_output.jsonl"
    
    cmd = [
        'python3', 'scripts/inference/laq_inference.py',
        '--input', str(test_jsonl),
        '--output', str(output_file),
        '--batch_size', '1',
        '--log_level', 'DEBUG'
    ]
    
    print(f"  执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("  ✅ 单批次推理成功")
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    results = [json.loads(line) for line in f if line.strip()]
                
                if results:
                    result = results[0]
                    print(f"  ✅ 结果: ID={result['id']}, Delta={result['delta']}")
                    return True
                else:
                    print("  ❌ 没有生成结果")
                    return False
            else:
                print("  ❌ 输出文件不存在")
                return False
        else:
            print(f"  ❌ 单批次推理失败")
            print(f"  标准输出: {result.stdout}")
            print(f"  错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 单批次推理异常: {e}")
        return False
    finally:
        cleanup_test_data(test_dir)

def main():
    """主测试函数"""
    print("🚀 测试修复后的LAQ推理脚本\n")
    
    tests = [
        ("单批次推理测试", test_single_batch),
        ("混合尺寸推理测试", test_inference_with_mixed_sizes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print('='*60)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！LAQ推理脚本修复成功。")
        return True
    else:
        print(f"❌ {total - passed} 个测试失败。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
