#!/usr/bin/env python3
"""
LAQ模型推理脚本

该脚本用于对预处理的帧对数据执行LAQ模型推理，生成动作量化码。

功能特性：
- 支持批量处理预处理的帧对数据
- 自动加载预训练LAQ模型
- 支持GPU加速推理
- 提供详细的进度显示和日志输出
- 支持灵活的配置参数和配置文件

作者: AI Assistant
日期: 2025-08-01
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import time

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms as T
from PIL import Image
import numpy as np

try:
    import yaml
except ImportError:
    yaml = None
    print("警告: 未安装PyYAML，配置文件功能将不可用。请运行: pip install PyYAML")

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / 'LAPA' / 'laq'))

try:
    from laq_model import LatentActionQuantization
except ImportError as e:
    print(f"错误：无法导入LAQ模型模块。请确保LAPA/laq目录存在且包含必要的模块。")
    print(f"详细错误：{e}")
    sys.exit(1)


class LAQInferenceDataset(Dataset):
    """LAQ推理数据集类"""
    
    def __init__(self, jsonl_file: str, transform=None):
        """
        初始化数据集
        
        Args:
            jsonl_file: JSONL格式的数据文件路径
            transform: 图像变换函数
        """
        self.data = []
        self.transform = transform
        
        # 读取JSONL数据
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    self.data.append(json.loads(line))
        
        logging.info(f"加载了 {len(self.data)} 个帧对数据")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        """获取单个数据项"""
        item = self.data[idx]

        # 加载图像对
        max_retries = 3
        for attempt in range(max_retries):
            try:
                img1 = Image.open(item['image']).convert('RGB')
                img2 = Image.open(item['next_image']).convert('RGB')
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    logging.error(f"加载图像失败 {item['image']} 或 {item['next_image']}: {e}")
                    logging.warning(f"使用黑色图像作为fallback")
                    # 返回黑色图像作为fallback
                    img1 = Image.new('RGB', (256, 256), (0, 0, 0))
                    img2 = Image.new('RGB', (256, 256), (0, 0, 0))
                else:
                    logging.warning(f"加载图像失败，重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(0.1)  # 短暂等待后重试

        # 应用变换
        try:
            if self.transform:
                img1 = self.transform(img1)
                img2 = self.transform(img2)
        except Exception as e:
            logging.error(f"图像变换失败: {e}")
            # 创建默认tensor
            img1 = torch.zeros(3, 256, 256)
            img2 = torch.zeros(3, 256, 256)

        # 组合为帧对 [C, 2, H, W]
        try:
            frame_pair = torch.stack([img1, img2], dim=1)
        except Exception as e:
            logging.error(f"创建帧对失败: {e}")
            frame_pair = torch.zeros(3, 2, 256, 256)

        return {
            'frame_pair': frame_pair,
            'metadata': item
        }


class LAQInferenceEngine:
    """LAQ推理引擎"""
    
    def __init__(self, config: Dict):
        """
        初始化推理引擎
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.transform = None
        
        # 设置日志
        self._setup_logging()
        
        # 初始化模型和变换
        self._load_model()
        self._setup_transform()
    
    def _setup_logging(self):
        """设置日志系统"""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        
        # 创建日志目录
        log_file = self.config.get('log_file', 'logs/laq_inference.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file)
            ]
        )
    
    def _load_model(self):
        """加载LAQ模型"""
        logging.info("正在加载LAQ模型...")
        
        try:
            # 创建模型实例
            self.model = LatentActionQuantization(
                dim=self.config['model']['dim'],
                quant_dim=self.config['model']['quant_dim'],
                codebook_size=self.config['model']['codebook_size'],
                image_size=self.config['model']['image_size'],
                patch_size=self.config['model']['patch_size'],
                spatial_depth=self.config['model']['spatial_depth'],
                temporal_depth=self.config['model']['temporal_depth'],
                dim_head=self.config['model']['dim_head'],
                heads=self.config['model']['heads'],
                code_seq_len=self.config['model']['code_seq_len'],
            )
            
            # 移动到设备
            self.model = self.model.to(self.device)
            
            # 加载预训练权重
            checkpoint_path = self.config['model']['checkpoint_path']
            if not os.path.exists(checkpoint_path):
                raise FileNotFoundError(f"模型检查点文件不存在: {checkpoint_path}")
            
            logging.info(f"从 {checkpoint_path} 加载模型权重...")
            self.model.load(checkpoint_path)
            
            # 设置为评估模式
            self.model.eval()
            
            logging.info(f"✓ LAQ模型加载成功，使用设备: {self.device}")
            logging.info(f"  - 模型参数: dim={self.config['model']['dim']}, "
                        f"codebook_size={self.config['model']['codebook_size']}")
            
        except Exception as e:
            logging.error(f"模型加载失败: {e}")
            raise
    
    def _setup_transform(self):
        """设置图像变换"""
        self.transform = T.Compose([
            T.Resize(self.config['model']['image_size']),
            T.ToTensor(),
        ])
        logging.info(f"✓ 图像变换设置完成，目标尺寸: {self.config['model']['image_size']}")
    
    def run_inference(self, input_file: str, output_file: str):
        """
        执行推理
        
        Args:
            input_file: 输入JSONL文件路径
            output_file: 输出JSONL文件路径
        """
        logging.info(f"开始推理任务...")
        logging.info(f"输入文件: {input_file}")
        logging.info(f"输出文件: {output_file}")
        
        # 创建数据集和数据加载器
        dataset = LAQInferenceDataset(input_file, self.transform)
        dataloader = DataLoader(
            dataset,
            batch_size=self.config['inference']['batch_size'],
            shuffle=False,
            num_workers=self.config['inference']['num_workers'],
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 执行推理
        results = []
        total_batches = len(dataloader)
        start_time = time.time()
        
        failed_batches = 0
        processed_samples = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc="推理进度")):
                batch_success = False
                max_retries = self.config.get('advanced', {}).get('max_retries', 3)

                for retry in range(max_retries):
                    try:
                        # 获取帧对数据
                        frame_pairs = batch['frame_pair'].to(self.device)  # [B, C, 2, H, W]
                        metadata_list = batch['metadata']

                        # 检查数据有效性
                        if torch.isnan(frame_pairs).any() or torch.isinf(frame_pairs).any():
                            logging.warning(f"批次 {batch_idx} 包含无效数据 (NaN/Inf)")
                            continue

                        # 执行推理，获取量化码
                        quantized_codes = self.model(frame_pairs, return_only_codebook_ids=True)

                        # 验证推理结果
                        if quantized_codes is None:
                            raise RuntimeError("模型推理返回None")

                        # 处理结果
                        batch_results = []
                        for i, codes in enumerate(quantized_codes):
                            try:
                                result = {
                                    'id': metadata_list['id'][i],
                                    'image': metadata_list['image'][i],
                                    'next_image': metadata_list['next_image'][i],
                                    'instruction': metadata_list['instruction'][i],
                                    'vision': metadata_list['vision'][i],
                                    'delta': [str(code.item()) for code in codes],
                                    'fields': "[instruction],[vision],delta"
                                }
                                batch_results.append(result)
                            except Exception as e:
                                logging.error(f"处理样本 {i} 结果时出错: {e}")
                                # 创建错误标记的结果
                                error_result = {
                                    'id': metadata_list['id'][i] if 'id' in metadata_list else f"error_{batch_idx}_{i}",
                                    'image': metadata_list.get('image', [''])[i] if 'image' in metadata_list else '',
                                    'next_image': metadata_list.get('next_image', [''])[i] if 'next_image' in metadata_list else '',
                                    'instruction': "Error during processing",
                                    'vision': "",
                                    'delta': ["0"],  # 默认错误码
                                    'fields': "[instruction],[vision],delta",
                                    'error': str(e)
                                }
                                batch_results.append(error_result)

                        results.extend(batch_results)
                        processed_samples += len(batch_results)
                        batch_success = True
                        break

                    except Exception as e:
                        if retry == max_retries - 1:
                            logging.error(f"批次 {batch_idx} 处理失败，已重试 {max_retries} 次: {e}")
                            failed_batches += 1

                            # 如果配置允许，继续处理下一批次
                            if not self.config.get('advanced', {}).get('continue_on_error', True):
                                raise
                        else:
                            logging.warning(f"批次 {batch_idx} 处理失败，重试 {retry + 1}/{max_retries}: {e}")
                            time.sleep(0.5)  # 等待后重试

                # 定期保存结果和清理内存
                if (batch_idx + 1) % 100 == 0:
                    logging.info(f"已处理 {batch_idx + 1}/{total_batches} 批次，成功样本: {processed_samples}，失败批次: {failed_batches}")

                    # 清理GPU缓存
                    if self.device.type == 'cuda':
                        torch.cuda.empty_cache()

        # 记录处理统计
        logging.info(f"批次处理完成: 总批次 {total_batches}，失败批次 {failed_batches}，成功样本 {processed_samples}")
        
        # 保存所有结果
        logging.info(f"保存推理结果到 {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
        
        # 统计信息
        end_time = time.time()
        total_time = end_time - start_time
        logging.info(f"✓ 推理完成!")
        logging.info(f"  - 处理样本数: {len(results)}")
        logging.info(f"  - 总耗时: {total_time:.2f}秒")
        logging.info(f"  - 平均速度: {len(results)/total_time:.2f}样本/秒")
        logging.info(f"  - 结果保存至: {output_file}")


def load_config(config_file: str) -> Dict:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    if not yaml:
        raise ImportError("需要安装PyYAML来支持配置文件功能: pip install PyYAML")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config


def get_default_config():
    """获取默认配置"""
    return {
        'model': {
            'dim': 1024,
            'quant_dim': 32,
            'codebook_size': 8,
            'image_size': 256,
            'patch_size': 32,
            'spatial_depth': 8,
            'temporal_depth': 8,
            'dim_head': 64,
            'heads': 16,
            'code_seq_len': 4,
            'checkpoint_path': 'models/laq_openx.pt'
        },
        'inference': {
            'batch_size': 32,
            'num_workers': 4
        },
        'log_level': 'INFO',
        'log_file': 'logs/laq_inference.log'
    }


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='LAQ模型推理脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用默认配置
  python scripts/inference/laq_inference.py --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl --output results/laq_results.jsonl

  # 使用配置文件
  python scripts/inference/laq_inference.py --config configs/laq_inference.yaml

  # 自定义参数
  python scripts/inference/laq_inference.py --input data/input.jsonl --output results/output.jsonl --batch_size 16 --codebook_size 1024
        """
    )

    # 配置文件参数
    parser.add_argument('--config', '-c',
                       help='配置文件路径（YAML格式）')

    # 必需参数（如果不使用配置文件）
    parser.add_argument('--input', '-i',
                       help='输入JSONL文件路径')
    parser.add_argument('--output', '-o',
                       help='输出JSONL文件路径')

    # 模型参数
    parser.add_argument('--checkpoint',
                       help='模型检查点文件路径')
    parser.add_argument('--codebook_size', type=int,
                       help='码本大小')
    parser.add_argument('--spatial_depth', type=int,
                       help='空间Transformer深度')
    parser.add_argument('--temporal_depth', type=int,
                       help='时间Transformer深度')
    parser.add_argument('--code_seq_len', type=int,
                       help='代码序列长度')

    # 推理参数
    parser.add_argument('--batch_size', type=int,
                       help='批处理大小')
    parser.add_argument('--num_workers', type=int,
                       help='数据加载器工作进程数')

    # 其他参数
    parser.add_argument('--log_level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--log_file',
                       help='日志文件路径')

    return parser.parse_args()


def merge_config_with_args(config: Dict, args: argparse.Namespace) -> Dict:
    """
    将命令行参数合并到配置中

    Args:
        config: 基础配置字典
        args: 命令行参数

    Returns:
        合并后的配置字典
    """
    # 模型参数
    if args.checkpoint:
        config['model']['checkpoint_path'] = args.checkpoint
    if args.codebook_size:
        config['model']['codebook_size'] = args.codebook_size
    if args.spatial_depth:
        config['model']['spatial_depth'] = args.spatial_depth
    if args.temporal_depth:
        config['model']['temporal_depth'] = args.temporal_depth
    if args.code_seq_len:
        config['model']['code_seq_len'] = args.code_seq_len

    # 推理参数
    if args.batch_size:
        config['inference']['batch_size'] = args.batch_size
    if args.num_workers:
        config['inference']['num_workers'] = args.num_workers

    # 其他参数
    if args.log_level:
        config['log_level'] = args.log_level
    if args.log_file:
        config['log_file'] = args.log_file

    return config


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 加载配置
    if args.config:
        # 从配置文件加载
        try:
            config = load_config(args.config)
            logging.info(f"从配置文件加载配置: {args.config}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    else:
        # 使用默认配置
        config = get_default_config()

    # 合并命令行参数
    config = merge_config_with_args(config, args)

    # 验证必需参数
    input_file = args.input if args.input else config.get('data', {}).get('input_file')
    output_file = args.output if args.output else config.get('data', {}).get('output_file')

    if not input_file:
        print("错误: 必须指定输入文件路径 (--input 或配置文件中的 data.input_file)")
        sys.exit(1)
    if not output_file:
        print("错误: 必须指定输出文件路径 (--output 或配置文件中的 data.output_file)")
        sys.exit(1)

    try:
        # 创建推理引擎并执行推理
        engine = LAQInferenceEngine(config)
        engine.run_inference(input_file, output_file)

    except KeyboardInterrupt:
        logging.info("用户中断推理过程")
        sys.exit(1)
    except Exception as e:
        logging.error(f"推理过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
