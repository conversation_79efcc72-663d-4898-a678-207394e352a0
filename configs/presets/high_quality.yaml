# LAQ高质量推理配置 - 适合生产环境和高质量结果
# 使用更深的模型和更大的码本以获得更好的表征质量

# 模型配置 - 优化质量
model:
  dim: 1024
  quant_dim: 32
  codebook_size: 8            # 匹配预训练模型
  image_size: 256
  patch_size: 32
  spatial_depth: 8            # 匹配预训练模型
  temporal_depth: 8           # 匹配预训练模型
  dim_head: 64
  heads: 16
  code_seq_len: 4
  checkpoint_path: "models/laq_openx.pt"

# 推理配置 - 平衡质量和速度
inference:
  batch_size: 16              # 较小的批处理大小以适应更大的模型
  num_workers: 4

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_high_quality_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_high_quality_inference.log"
