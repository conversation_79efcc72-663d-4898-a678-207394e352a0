# LAQ大规模推理配置 - 适合处理大型数据集
# 优化内存使用和处理效率

# 模型配置 - 平衡性能和资源使用
model:
  dim: 1024
  quant_dim: 32
  codebook_size: 512          # 中等码本大小
  image_size: 256
  patch_size: 32
  spatial_depth: 8
  temporal_depth: 8
  dim_head: 64
  heads: 16
  code_seq_len: 4
  checkpoint_path: "models/laq_openx.pt"

# 推理配置 - 优化大规模处理
inference:
  batch_size: 128             # 大批处理大小
  num_workers: 8              # 更多工作进程

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_large_scale_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_large_scale_inference.log"

# 高级配置 - 大规模处理优化
advanced:
  continue_on_error: true     # 遇到错误继续处理
  max_retries: 3
  progress_bar: true
  update_frequency: 50        # 更频繁的进度更新
