# LAQ快速推理配置 - 适合测试和快速验证
# 使用较小的模型深度和较大的批处理大小以提高速度

# 模型配置 - 优化速度
model:
  dim: 1024
  quant_dim: 32
  codebook_size: 8            # 较小的码本大小
  image_size: 256
  patch_size: 32
  spatial_depth: 4            # 减少层数以提高速度
  temporal_depth: 4           # 减少层数以提高速度
  dim_head: 64
  heads: 16
  code_seq_len: 4
  checkpoint_path: "models/laq_openx.pt"

# 推理配置 - 优化吞吐量
inference:
  batch_size: 64              # 较大的批处理大小
  num_workers: 8              # 更多工作进程

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_fast_inference_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_fast_inference.log"
