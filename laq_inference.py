#!/usr/bin/env python3
"""
LAQ模型推理脚本

该脚本用于对预处理的帧对数据执行LAQ模型推理，生成动作量化码。

功能特性：
- 支持批量处理预处理的帧对数据
- 自动加载预训练LAQ模型
- 支持GPU加速推理
- 提供详细的进度显示和日志输出
- 支持灵活的配置参数

作者: AI Assistant
日期: 2025-08-01
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import time

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms as T
from PIL import Image
import numpy as np

# 添加LAQ模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'LAPA', 'laq'))

try:
    from laq_model import LatentActionQuantization
except ImportError as e:
    print(f"错误：无法导入LAQ模型模块。请确保LAPA/laq目录存在且包含必要的模块。")
    print(f"详细错误：{e}")
    sys.exit(1)


class LAQInferenceDataset(Dataset):
    """LAQ推理数据集类"""
    
    def __init__(self, jsonl_file: str, transform=None):
        """
        初始化数据集
        
        Args:
            jsonl_file: JSONL格式的数据文件路径
            transform: 图像变换函数
        """
        self.data = []
        self.transform = transform
        
        # 读取JSONL数据
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    self.data.append(json.loads(line))
        
        logging.info(f"加载了 {len(self.data)} 个帧对数据")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        """获取单个数据项"""
        item = self.data[idx]
        
        # 加载图像对
        try:
            img1 = Image.open(item['image']).convert('RGB')
            img2 = Image.open(item['next_image']).convert('RGB')
        except Exception as e:
            logging.error(f"加载图像失败 {item['image']} 或 {item['next_image']}: {e}")
            # 返回黑色图像作为fallback
            img1 = Image.new('RGB', (256, 256), (0, 0, 0))
            img2 = Image.new('RGB', (256, 256), (0, 0, 0))
        
        # 应用变换
        if self.transform:
            img1 = self.transform(img1)
            img2 = self.transform(img2)
        
        # 组合为帧对 [C, 2, H, W]
        frame_pair = torch.stack([img1, img2], dim=1)
        
        return {
            'frame_pair': frame_pair,
            'metadata': item
        }


class LAQInferenceEngine:
    """LAQ推理引擎"""
    
    def __init__(self, config: Dict):
        """
        初始化推理引擎
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.transform = None
        
        # 设置日志
        self._setup_logging()
        
        # 初始化模型和变换
        self._load_model()
        self._setup_transform()
    
    def _setup_logging(self):
        """设置日志系统"""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.config.get('log_file', 'laq_inference.log'))
            ]
        )
    
    def _load_model(self):
        """加载LAQ模型"""
        logging.info("正在加载LAQ模型...")
        
        try:
            # 创建模型实例
            self.model = LatentActionQuantization(
                dim=self.config['model']['dim'],
                quant_dim=self.config['model']['quant_dim'],
                codebook_size=self.config['model']['codebook_size'],
                image_size=self.config['model']['image_size'],
                patch_size=self.config['model']['patch_size'],
                spatial_depth=self.config['model']['spatial_depth'],
                temporal_depth=self.config['model']['temporal_depth'],
                dim_head=self.config['model']['dim_head'],
                heads=self.config['model']['heads'],
                code_seq_len=self.config['model']['code_seq_len'],
            )
            
            # 移动到设备
            self.model = self.model.to(self.device)
            
            # 加载预训练权重
            checkpoint_path = self.config['model']['checkpoint_path']
            if not os.path.exists(checkpoint_path):
                raise FileNotFoundError(f"模型检查点文件不存在: {checkpoint_path}")
            
            logging.info(f"从 {checkpoint_path} 加载模型权重...")
            self.model.load(checkpoint_path)
            
            # 设置为评估模式
            self.model.eval()
            
            logging.info(f"✓ LAQ模型加载成功，使用设备: {self.device}")
            logging.info(f"  - 模型参数: dim={self.config['model']['dim']}, "
                        f"codebook_size={self.config['model']['codebook_size']}")
            
        except Exception as e:
            logging.error(f"模型加载失败: {e}")
            raise
    
    def _setup_transform(self):
        """设置图像变换"""
        self.transform = T.Compose([
            T.Resize(self.config['model']['image_size']),
            T.ToTensor(),
        ])
        logging.info(f"✓ 图像变换设置完成，目标尺寸: {self.config['model']['image_size']}")
    
    def run_inference(self, input_file: str, output_file: str):
        """
        执行推理
        
        Args:
            input_file: 输入JSONL文件路径
            output_file: 输出JSONL文件路径
        """
        logging.info(f"开始推理任务...")
        logging.info(f"输入文件: {input_file}")
        logging.info(f"输出文件: {output_file}")
        
        # 创建数据集和数据加载器
        dataset = LAQInferenceDataset(input_file, self.transform)
        dataloader = DataLoader(
            dataset,
            batch_size=self.config['inference']['batch_size'],
            shuffle=False,
            num_workers=self.config['inference']['num_workers'],
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 执行推理
        results = []
        total_batches = len(dataloader)
        start_time = time.time()
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc="推理进度")):
                try:
                    # 获取帧对数据
                    frame_pairs = batch['frame_pair'].to(self.device)  # [B, C, 2, H, W]
                    metadata_list = batch['metadata']
                    
                    # 执行推理，获取量化码
                    quantized_codes = self.model(frame_pairs, return_only_codebook_ids=True)
                    
                    # 处理结果
                    for i, codes in enumerate(quantized_codes):
                        result = {
                            'id': metadata_list['id'][i],
                            'image': metadata_list['image'][i],
                            'next_image': metadata_list['next_image'][i],
                            'instruction': metadata_list['instruction'][i],
                            'vision': metadata_list['vision'][i],
                            'delta': [str(code.item()) for code in codes],
                            'fields': "[instruction],[vision],delta"
                        }
                        results.append(result)
                    
                    # 定期保存结果（避免内存溢出）
                    if (batch_idx + 1) % 100 == 0:
                        logging.info(f"已处理 {batch_idx + 1}/{total_batches} 批次")
                
                except Exception as e:
                    logging.error(f"处理批次 {batch_idx} 时出错: {e}")
                    continue
        
        # 保存所有结果
        logging.info(f"保存推理结果到 {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
        
        # 统计信息
        end_time = time.time()
        total_time = end_time - start_time
        logging.info(f"✓ 推理完成!")
        logging.info(f"  - 处理样本数: {len(results)}")
        logging.info(f"  - 总耗时: {total_time:.2f}秒")
        logging.info(f"  - 平均速度: {len(results)/total_time:.2f}样本/秒")
        logging.info(f"  - 结果保存至: {output_file}")


def get_default_config():
    """获取默认配置"""
    return {
        'model': {
            'dim': 1024,
            'quant_dim': 32,
            'codebook_size': 8,
            'image_size': 256,
            'patch_size': 32,
            'spatial_depth': 8,
            'temporal_depth': 8,
            'dim_head': 64,
            'heads': 16,
            'code_seq_len': 4,
            'checkpoint_path': 'models/laq_openx.pt'
        },
        'inference': {
            'batch_size': 32,
            'num_workers': 4
        },
        'log_level': 'INFO',
        'log_file': 'laq_inference.log'
    }


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='LAQ模型推理脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python laq_inference.py --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl --output results/laq_results.jsonl
  python laq_inference.py --input data/input.jsonl --output results/output.jsonl --batch_size 16 --codebook_size 1024
        """
    )
    
    # 必需参数
    parser.add_argument('--input', '-i', required=True,
                       help='输入JSONL文件路径')
    parser.add_argument('--output', '-o', required=True,
                       help='输出JSONL文件路径')
    
    # 模型参数
    parser.add_argument('--checkpoint', default='models/laq_openx.pt',
                       help='模型检查点文件路径 (默认: models/laq_openx.pt)')
    parser.add_argument('--codebook_size', type=int, default=8,
                       help='码本大小 (默认: 8)')
    parser.add_argument('--spatial_depth', type=int, default=8,
                       help='空间Transformer深度 (默认: 8)')
    parser.add_argument('--temporal_depth', type=int, default=8,
                       help='时间Transformer深度 (默认: 8)')
    parser.add_argument('--code_seq_len', type=int, default=4,
                       help='代码序列长度 (默认: 4)')
    
    # 推理参数
    parser.add_argument('--batch_size', type=int, default=32,
                       help='批处理大小 (默认: 32)')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载器工作进程数 (默认: 4)')
    
    # 其他参数
    parser.add_argument('--log_level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--log_file', default='laq_inference.log',
                       help='日志文件路径 (默认: laq_inference.log)')
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 构建配置
    config = get_default_config()
    config['model']['checkpoint_path'] = args.checkpoint
    config['model']['codebook_size'] = args.codebook_size
    config['model']['spatial_depth'] = args.spatial_depth
    config['model']['temporal_depth'] = args.temporal_depth
    config['model']['code_seq_len'] = args.code_seq_len
    config['inference']['batch_size'] = args.batch_size
    config['inference']['num_workers'] = args.num_workers
    config['log_level'] = args.log_level
    config['log_file'] = args.log_file
    
    try:
        # 创建推理引擎并执行推理
        engine = LAQInferenceEngine(config)
        engine.run_inference(args.input, args.output)
        
    except KeyboardInterrupt:
        logging.info("用户中断推理过程")
        sys.exit(1)
    except Exception as e:
        logging.error(f"推理过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
